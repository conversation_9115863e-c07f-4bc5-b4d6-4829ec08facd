<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案 - 多模态智能评测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
        }

        .logo-section {
            background: linear-gradient(135deg, #4a5568, #2d3748);
            color: white;
            padding: 15px 30px;
            clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 0 100%);
            margin-right: 20px;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-tabs {
            display: flex;
            margin-left: auto;
        }

        .nav-tab {
            background: #10b981;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-tab:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        .nav-tab:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            border-right: none;
        }

        .nav-tab:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .left-content {
            padding-right: 20px;
        }

        .right-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 标题样式 */
        .main-title {
            font-size: 48px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 20px;
            position: relative;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 2px;
        }

        /* 核心理念 */
        .core-concept {
            margin-bottom: 40px;
        }

        .concept-text {
            font-size: 18px;
            color: #047857;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .highlight-box {
            background: #10b981;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        /* 解决方案部分 */
        .solution-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 36px;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 15px;
        }

        .section-subtitle {
            font-size: 20px;
            color: #047857;
            margin-bottom: 25px;
            font-weight: 600;
        }

        /* 用户群体卡片 */
        .user-groups {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .user-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
            border-left: 4px solid #10b981;
            transition: all 0.3s ease;
        }

        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        }

        .user-title {
            font-size: 20px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .user-title::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 10px;
        }

        .user-description {
            font-size: 16px;
            color: #374151;
            line-height: 1.6;
        }

        /* 手机展示图片 */
        .phone-display {
            position: relative;
            max-width: 400px;
            width: 100%;
        }

        .phone-image {
            width: 100%;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .phone-image:hover {
            transform: scale(1.05);
        }

        /* 装饰元素 */
        .decoration-circle {
            position: absolute;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            opacity: 0.1;
            top: -20px;
            right: -20px;
            z-index: -1;
        }

        /* 技术优势展示 */
        .tech-advantages {
            margin-top: 40px;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.1);
            border: 1px solid #d1fae5;
        }

        .advantages-title {
            font-size: 24px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .advantages-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: #10b981;
            border-radius: 2px;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .advantage-item {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .advantage-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
        }

        .advantage-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.2);
        }

        .advantage-icon {
            width: 50px;
            height: 50px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
        }

        .advantage-title {
            font-size: 16px;
            font-weight: 600;
            color: #065f46;
            margin-bottom: 8px;
        }

        .advantage-desc {
            font-size: 14px;
            color: #047857;
            line-height: 1.5;
        }

        /* 数据统计展示 */
        .stats-section {
            margin-top: 40px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 16px;
            padding: 30px;
            color: white;
            text-align: center;
        }

        .stats-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 25px;
            opacity: 0.95;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 流程步骤 */
        .process-section {
            margin-top: 40px;
        }

        .process-title {
            font-size: 24px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 30px;
            text-align: center;
        }

        .process-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin: 30px 0;
        }

        .process-steps::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #d1fae5;
            z-index: 1;
        }

        .process-step {
            background: white;
            border: 3px solid #10b981;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .process-step:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .step-number {
            font-size: 18px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 2px;
        }

        .step-icon {
            font-size: 16px;
            color: #047857;
        }

        .step-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .step-label {
            text-align: center;
            font-size: 14px;
            color: #047857;
            font-weight: 600;
            width: 80px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .main-title {
                font-size: 36px;
            }

            .section-title {
                font-size: 28px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                padding: 10px 15px;
                font-size: 12px;
            }

            .user-groups {
                gap: 15px;
            }

            .user-card {
                padding: 20px;
            }

            .advantages-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .process-steps {
                flex-direction: column;
                gap: 20px;
            }

            .process-steps::before {
                display: none;
            }

            .step-labels {
                flex-direction: column;
                gap: 20px;
                align-items: center;
            }

            .tech-advantages,
            .stats-section,
            .process-section {
                margin-top: 30px;
                padding: 20px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .user-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .user-card:nth-child(1) { animation-delay: 0.1s; }
        .user-card:nth-child(2) { animation-delay: 0.2s; }
        .user-card:nth-child(3) { animation-delay: 0.3s; }

        .left-content {
            animation: slideInLeft 0.8s ease forwards;
        }

        .right-content {
            animation: slideInRight 0.8s ease forwards;
        }

        .advantage-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .advantage-item:nth-child(1) { animation-delay: 0.1s; }
        .advantage-item:nth-child(2) { animation-delay: 0.2s; }
        .advantage-item:nth-child(3) { animation-delay: 0.3s; }
        .advantage-item:nth-child(4) { animation-delay: 0.4s; }

        .stat-number {
            animation: countUp 0.8s ease forwards;
        }

        .process-step {
            animation: fadeInUp 0.6s ease forwards;
        }

        .process-step:nth-child(1) { animation-delay: 0.2s; }
        .process-step:nth-child(2) { animation-delay: 0.4s; }
        .process-step:nth-child(3) { animation-delay: 0.6s; }
        .process-step:nth-child(4) { animation-delay: 0.8s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="logo-section">
                <div class="logo-text">TEXT COMPANY NAME & LOGO</div>
            </div>
            <div class="nav-tabs">
                <a href="#" class="nav-tab">项目概述</a>
                <a href="#" class="nav-tab">项目内容</a>
                <a href="#" class="nav-tab">项目发展</a>
                <a href="#" class="nav-tab">团队介绍</a>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="left-content">
                <!-- 主标题 -->
                <h1 class="main-title">解决方案</h1>

                <!-- 核心理念 -->
                <div class="core-concept">
                    <p class="concept-text">
                        核心理念：从"单点评估"走向"全景洞察"，从"结果评判"走向"过程赋能"。
                    </p>
                    <div class="highlight-box">
                        科大讯飞AI平台12个核心接口深度集成
                    </div>
                </div>

                <!-- 解决方案改革 -->
                <div class="solution-section">
                    <h2 class="section-title">解决方案</h2>
                    <h3 class="section-subtitle">改革</h3>

                    <!-- 用户群体价值主张 -->
                    <div class="user-groups">
                        <div class="user-card">
                            <h4 class="user-title">对于求职者</h4>
                            <p class="user-description">
                                提供一个公平、沉浸、个性化的自我展示与能力提升平台。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于企业</h4>
                            <p class="user-description">
                                提供一个高效、精准、数据驱动的智能与人才筛选工具。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于面试官</h4>
                            <p class="user-description">
                                提供一个智能、标准化的面试辅助与决策支持系统。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 技术优势展示 -->
                <div class="tech-advantages">
                    <h3 class="advantages-title">技术优势</h3>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <div class="advantage-icon">🧠</div>
                            <div class="advantage-title">AI深度集成</div>
                            <div class="advantage-desc">科大讯飞12个核心API接口，覆盖语音、视觉、文本全维度</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">🔄</div>
                            <div class="advantage-title">多模态融合</div>
                            <div class="advantage-desc">语音+视频+文本三维度智能分析，准确率提升35%</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">⚡</div>
                            <div class="advantage-title">实时处理</div>
                            <div class="advantage-desc">毫秒级响应，支持大规模并发，延迟<100ms</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">🛡️</div>
                            <div class="advantage-title">安全可靠</div>
                            <div class="advantage-desc">AES-256加密，HTTPS传输，等保三级认证</div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计展示 -->
                <div class="stats-section">
                    <h3 class="stats-title">平台核心数据</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">12+</span>
                            <span class="stat-label">AI接口集成</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">95%</span>
                            <span class="stat-label">分析准确率</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100ms</span>
                            <span class="stat-label">响应延迟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1000+</span>
                            <span class="stat-label">企业客户</span>
                        </div>
                    </div>
                </div>

                <!-- 实施流程 -->
                <div class="process-section">
                    <h3 class="process-title">实施流程</h3>
                    <div class="process-steps">
                        <div class="process-step">
                            <div class="step-number">1</div>
                            <div class="step-icon">📋</div>
                        </div>
                        <div class="process-step">
                            <div class="step-number">2</div>
                            <div class="step-icon">🎯</div>
                        </div>
                        <div class="process-step">
                            <div class="step-number">3</div>
                            <div class="step-icon">🚀</div>
                        </div>
                        <div class="process-step">
                            <div class="step-number">4</div>
                            <div class="step-icon">📊</div>
                        </div>
                    </div>
                    <div class="step-labels">
                        <div class="step-label">需求分析</div>
                        <div class="step-label">方案定制</div>
                        <div class="step-label">系统部署</div>
                        <div class="step-label">效果评估</div>
                    </div>
                </div>
            </div>

            <div class="right-content">
                <!-- 手机展示图片 -->
                <div class="phone-display">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDMwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTAwIiByeD0iMzAiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxyZWN0IHg9IjIwIiB5PSI4MCIgd2lkdGg9IjI2MCIgaGVpZ2h0PSIzNDAiIHJ4PSIxNSIgZmlsbD0iIzEwYjk4MSIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0NTAiIHI9IjMwIiBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMyIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0MCIgcng9IjQwIiByeT0iOCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjMiLz4KPHN2ZyB4PSI0MCIgeT0iMTAwIiB3aWR0aD0iMjIwIiBoZWlnaHQ9IjMwMCI+CjxyZWN0IHdpZHRoPSIyMjAiIGhlaWdodD0iNjAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4yIiByeD0iOCIvPgo8cmVjdCB5PSI4MCIgd2lkdGg9IjIyMCIgaGVpZ2h0PSI0MCIgZmlsbD0iI2ZmZmZmZiIgZmlsbC1vcGFjaXR5PSIwLjE1IiByeD0iOCIvPgo8cmVjdCB5PSIxNDAiIHdpZHRoPSIyMjAiIGhlaWdodD0iNDAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xNSIgcng9IjgiLz4KPHJlY3QgeT0iMjAwIiB3aWR0aD0iMjIwIiGhlaWdodD0iNDAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xNSIgcng9IjgiLz4KPHJlY3QgeT0iMjYwIiB3aWR0aD0iMTYwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMDU5NjY5IiByeD0iMTUiLz4KPHRleHQgeD0iMTEwIiB5PSIxMzAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkFJ6Z2i6K+V57O757uf8J+kljwvdGV4dD4KPHRleHQgeD0iMTEwIiB5PSIxNzAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtc2l6ZT0iMTIiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWkmuaooeaAgeWIhuaekDwvdGV4dD4KPHRleHQgeD0iMTEwIiB5PSIyMzAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtc2l6ZT0iMTIiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWunuaXtuWIhuaekOWPjemlojwvdGV4dD4KPHRleHQgeD0iMTEwIiB5PSIyODAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtc2l6ZT0iMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuW8gOWni+mdouivlTwvdGV4dD4KPC9zdmc+Cjwvc3ZnPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudDBfbGluZWFyXzFfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMzAwIiB5Mj0iNTAwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM2M2IzZWQiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMzc4M2Y0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg=="
                         alt="智能面试系统手机界面"
                         class="phone-image">
                    <div class="decoration-circle"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加JavaScript增强交互 -->
    <script>
        // 数字动画效果
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = number.textContent;
                const isPercentage = target.includes('%');
                const isTime = target.includes('ms');
                const isPlus = target.includes('+');

                let finalValue = parseInt(target.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = finalValue / 50;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= finalValue) {
                        current = finalValue;
                        clearInterval(timer);
                    }

                    let displayValue = Math.floor(current);
                    if (isPercentage) displayValue += '%';
                    if (isTime) displayValue += 'ms';
                    if (isPlus) displayValue += '+';

                    number.textContent = displayValue;
                }, 50);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', () => {
            setTimeout(animateNumbers, 1000);
        });

        // 添加滚动视差效果
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.decoration-circle');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // 添加鼠标跟随效果
        document.addEventListener('mousemove', (e) => {
            const cursor = document.querySelector('.decoration-circle');
            if (cursor) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                cursor.style.transform = `translate(${x * 20}px, ${y * 20}px)`;
            }
        });

        // 添加卡片点击效果
        document.querySelectorAll('.user-card, .advantage-item').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 添加导航标签点击效果
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>

    <style>
        /* 添加活跃状态样式 */
        .nav-tab.active {
            background: #059669 !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.4);
        }

        /* 添加光标样式 */
        .user-card, .advantage-item, .process-step {
            cursor: pointer;
        }

        /* 添加选中效果 */
        .user-card:active, .advantage-item:active {
            transform: scale(0.95) !important;
        }
    </style>
</body>
</html>
