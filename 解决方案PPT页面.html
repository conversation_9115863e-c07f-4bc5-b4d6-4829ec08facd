<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案 - 多模态智能评测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
        }

        .logo-section {
            background: linear-gradient(135deg, #4a5568, #2d3748);
            color: white;
            padding: 15px 30px;
            clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 0 100%);
            margin-right: 20px;
        }

        .logo-text {
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-tabs {
            display: flex;
            margin-left: auto;
        }

        .nav-tab {
            background: #10b981;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-tab:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        .nav-tab:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            border-right: none;
        }

        .nav-tab:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .left-content {
            padding-right: 20px;
        }

        .right-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 标题样式 */
        .main-title {
            font-size: 48px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 20px;
            position: relative;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 2px;
        }

        /* 核心理念 */
        .core-concept {
            margin-bottom: 40px;
        }

        .concept-text {
            font-size: 18px;
            color: #047857;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .highlight-box {
            background: #10b981;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        /* 解决方案部分 */
        .solution-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 36px;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 15px;
        }

        .section-subtitle {
            font-size: 20px;
            color: #047857;
            margin-bottom: 25px;
            font-weight: 600;
        }

        /* 用户群体卡片 */
        .user-groups {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .user-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
            border-left: 4px solid #10b981;
            transition: all 0.3s ease;
        }

        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        }

        .user-title {
            font-size: 20px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .user-title::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 10px;
        }

        .user-description {
            font-size: 16px;
            color: #374151;
            line-height: 1.6;
        }

        /* 手机展示图片 */
        .phone-display {
            position: relative;
            max-width: 400px;
            width: 100%;
        }

        .phone-image {
            width: 100%;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .phone-image:hover {
            transform: scale(1.05);
        }

        /* 装饰元素 */
        .decoration-circle {
            position: absolute;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            opacity: 0.1;
            top: -20px;
            right: -20px;
            z-index: -1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .main-title {
                font-size: 36px;
            }

            .section-title {
                font-size: 28px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                padding: 10px 15px;
                font-size: 12px;
            }

            .user-groups {
                gap: 15px;
            }

            .user-card {
                padding: 20px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .user-card:nth-child(1) { animation-delay: 0.1s; }
        .user-card:nth-child(2) { animation-delay: 0.2s; }
        .user-card:nth-child(3) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="logo-section">
                <div class="logo-text">TEXT COMPANY NAME & LOGO</div>
            </div>
            <div class="nav-tabs">
                <a href="#" class="nav-tab">项目概述</a>
                <a href="#" class="nav-tab">项目内容</a>
                <a href="#" class="nav-tab">项目发展</a>
                <a href="#" class="nav-tab">团队介绍</a>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="left-content">
                <!-- 主标题 -->
                <h1 class="main-title">解决方案</h1>

                <!-- 核心理念 -->
                <div class="core-concept">
                    <p class="concept-text">
                        核心理念：从"单点评估"走向"全景洞察"，从"结果评判"走向"过程赋能"。
                    </p>
                    <div class="highlight-box">
                        科大讯飞AI平台12个核心接口深度集成
                    </div>
                </div>

                <!-- 解决方案改革 -->
                <div class="solution-section">
                    <h2 class="section-title">解决方案</h2>
                    <h3 class="section-subtitle">改革</h3>

                    <!-- 用户群体价值主张 -->
                    <div class="user-groups">
                        <div class="user-card">
                            <h4 class="user-title">对于求职者</h4>
                            <p class="user-description">
                                提供一个公平、沉浸、个性化的自我展示与能力提升平台。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于企业</h4>
                            <p class="user-description">
                                提供一个高效、精准、数据驱动的智能与人才筛选工具。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于面试官</h4>
                            <p class="user-description">
                                提供一个智能、标准化的面试辅助与决策支持系统。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-content">
                <!-- 手机展示图片 -->
                <div class="phone-display">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDMwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTAwIiByeD0iMzAiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxyZWN0IHg9IjIwIiB5PSI4MCIgd2lkdGg9IjI2MCIgaGVpZ2h0PSIzNDAiIHJ4PSIxNSIgZmlsbD0iIzEwYjk4MSIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0NTAiIHI9IjMwIiBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMyIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0MCIgcng9IjQwIiByeT0iOCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjMiLz4KPHN2ZyB4PSI0MCIgeT0iMTAwIiB3aWR0aD0iMjIwIiBoZWlnaHQ9IjMwMCI+CjxyZWN0IHdpZHRoPSIyMjAiIGhlaWdodD0iNjAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4yIiByeD0iOCIvPgo8cmVjdCB5PSI4MCIgd2lkdGg9IjIyMCIgaGVpZ2h0PSI0MCIgZmlsbD0iI2ZmZmZmZiIgZmlsbC1vcGFjaXR5PSIwLjE1IiByeD0iOCIvPgo8cmVjdCB5PSIxNDAiIHdpZHRoPSIyMjAiIGhlaWdodD0iNDAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xNSIgcng9IjgiLz4KPHJlY3QgeT0iMjAwIiB3aWR0aD0iMjIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMTUiIHJ4PSI4Ii8+CjxyZWN0IHk9IjI2MCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzA1OTY2OSIgcng9IjE1Ii8+Cjwvc3ZnPgo8L3N2Zz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQwX2xpbmVhcl8xXzEiIHgxPSIwIiB5MT0iMCIgeDI9IjMwMCIgeTI9IjUwMCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNjNiM2VkIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzM3ODNmNCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=" 
                         alt="智能面试系统手机界面" 
                         class="phone-image">
                    <div class="decoration-circle"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
