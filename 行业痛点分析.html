<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业痛点分析 - 多模态智能评测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e6f7e9 0%, #c3e6d3 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,120,50,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
        }

        .header {
            background: linear-gradient(135deg, #1a9c5b 0%, #0d7a43 100%);
            color: white;
            padding: 30px 40px;
            text-align: center;
            position: relative;
            flex-shrink: 0;
        }

        .header::before {
            content: ''哦那个
            position: absolute;
            top: -50px;
            left: -50px;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px 40px;
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .section {
            margin-bottom: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 30px;
            background: linear-gradient(135deg, #1a9c5b 0%, #0d7a43 100%);
            border-radius: 3px;
        }

        .main-content {
            display: flex;
            flex-direction: row;
            gap: 30px;
            flex: 1;
        }

        .pain-points {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
        }

        .pain-point {
            background: #f0f9f3;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #1a9c5b;
            transition: transform 0.3s ease;
            flex: 1;
        }

        .pain-point:hover {
            transform: translateX(5px);
        }

        .pain-point h3 {
            color: #1a9c5b;
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .pain-point .percentage {
            font-size: 2em;
            font-weight: bold;
            color: #1a9c5b;
            margin-bottom: 8px;
        }

        .pain-point p {
            color: #666;
            line-height: 1.4;
            font-size: 0.9em;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
            font-size: 0.9em;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #1a9c5b 0%, #0d7a43 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 1em;
        }

        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .traditional {
            color: #e74c3c;
            font-weight: bold;
        }

        .intelligent {
            color: #1a9c5b;
            font-weight: bold;
        }

        .chart-container {
            background: #f0f9f3;
            padding: 20px;
            border-radius: 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chart-title {
            text-align: center;
            font-size: 1.2em;
            color: #333;
            margin-bottom: 15px;
        }

        .chart-bars {
            display: flex;
            justify-content: space-around;
            align-items: end;
            flex: 1;
            margin-bottom: 15px;
        }

        .chart-bar {
            width: 60px;
            background: linear-gradient(to top, #1a9c5b, #0d7a43);
            border-radius: 10px 10px 0 0;
            position: relative;
            display: flex;
            align-items: end;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1em;
        }

        .chart-bar.bar-90 { height: 90%; }
        .chart-bar.bar-70 { height: 70%; }
        .chart-bar.bar-85 { height: 85%; }

        .chart-labels {
            display: flex;
            justify-content: space-around;
            margin-top: 10px;
        }

        .chart-label {
            text-align: center;
            font-weight: bold;
            color: #333;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>行业痛点</h1>
            <p class="subtitle">多模态智能评测系统解决方案</p>
        </div>

        <div class="content">
            <div class="section">
                <h2 class="section-title">当前招聘的困境</h2>
                <p style="font-size: 1em; color: #666; margin-bottom: 20px; line-height: 1.6;">
                    <strong>（引用数据）：</strong>传统面试主观性强达90%，效率低下70%，标准不统一85%
                </p>

                <div class="main-content">
                    <div class="pain-points">
                        <div class="pain-point">
                            <h3>主观性强</h3>
                            <div class="percentage">90%</div>
                            <p>传统面试过度依赖面试官个人经验和主观判断，缺乏客观评价标准，容易产生偏见和误判。</p>
                        </div>
                        <div class="pain-point">
                            <h3>效率低下</h3>
                            <div class="percentage">70%</div>
                            <p>人工筛选简历、安排面试、评估候选人耗时巨大，招聘周期长，人力成本高昂。</p>
                        </div>
                        <div class="pain-point">
                            <h3>标准不统一</h3>
                            <div class="percentage">85%</div>
                            <p>不同面试官评价标准差异巨大，同一候选人可能得到截然不同的评价结果，缺乏一致性。</p>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-title">传统招聘方式问题严重程度</div>
                        <div class="chart-bars">
                            <div class="chart-bar bar-90">90%</div>
                            <div class="chart-bar bar-70">70%</div>
                            <div class="chart-bar bar-85">85%</div>
                        </div>
                        <div class="chart-labels">
                            <div class="chart-label">主观性强</div>
                            <div class="chart-label">效率低下</div>
                            <div class="chart-label">标准不统一</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">传统 vs 智能对比图</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>评估维度</th>
                            <th>传统招聘方式</th>
                            <th>多模态智能评测系统</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>客观性</strong></td>
                            <td class="traditional">主观性强达90%</td>
                            <td class="intelligent">基于AI算法，客观评测95%</td>
                        </tr>
                        <tr>
                            <td><strong>效率</strong></td>
                            <td class="traditional">人工处理，效率低70%</td>
                            <td class="intelligent">自动化处理，效率提升300%</td>
                        </tr>
                        <tr>
                            <td><strong>标准化</strong></td>
                            <td class="traditional">标准不统一85%</td>
                            <td class="intelligent">统一评测标准，一致性98%</td>
                        </tr>
                        <tr>
                            <td><strong>成本</strong></td>
                            <td class="traditional">人力成本高，时间成本大</td>
                            <td class="intelligent">降低人力成本60%，时间成本80%</td>
                        </tr>
                        <tr>
                            <td><strong>覆盖面</strong></td>
                            <td class="traditional">单一维度评估</td>
                            <td class="intelligent">语音+视频+文本多模态评估</td>
                        </tr>
                        <tr>
                            <td><strong>数据分析</strong></td>
                            <td class="traditional">缺乏数据支撑</td>
                            <td class="intelligent">大数据分析，精准人才画像</td>
                        </tr>
                        <tr>
                            <td><strong>用户体验</strong></td>
                            <td class="traditional">流程繁琐，体验差</td>
                            <td class="intelligent">智能化流程，体验优化</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
