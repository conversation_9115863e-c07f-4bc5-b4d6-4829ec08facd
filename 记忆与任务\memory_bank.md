# 多模态智能评测系统 - 记忆中枢 v1.0

## 项目概览

### 基本信息
- **项目名称**: 基于科大讯飞人工智能平台的多模态智能评测系统
- **比赛**: 第十九届"软件杯"大赛
- **队伍**: 爪哇族 (SH-06)
- **指导教师**: 王勇、邢飞亚
- **项目定位**: AI驱动的智能面试与评测平台

### 核心价值主张
- 基于科大讯飞AI平台的多模态分析能力
- 集成12个讯飞API接口，涵盖语音、视频、文本、情感等维度
- 为企业HR和求职者提供智能化面试解决方案
- 支持多端部署（Web、移动端、小程序）

## 技术架构

### 整体架构
- **架构模式**: 前后端分离 + 微服务思想的混合架构
- **部署模式**: 多端开发（Web、H5、小程序）
- **核心特色**: 横向布局的分层架构设计

### 技术栈
#### 前端技术栈
- **Web管理端**: Vue3 + TypeScript + Element Plus
- **移动端**: Uni-app + Vue3 + UnoCSS
- **小程序**: 微信小程序（通过Uni-app一套代码多端运行）

#### 后端技术栈
- **框架**: Spring Boot + RuoYi-Vue-Plus
- **数据库**: MySQL 8.0 (主数据库) + Redis 7.x (缓存)
- **存储**: MinIO (对象存储)
- **搜索**: Elasticsearch
- **Web服务器**: Nginx
- **容器化**: Docker + Kubernetes

#### AI能力层
- **核心引擎**: 科大讯飞人工智能平台
- **集成接口**: 12个API接口
  - 认知与理解(5个): 星火大模型、NLP、语义理解、智能问答、图像理解
  - 语音处理(4个): ASR语音识别、TTS语音合成、语音评测、情感分析
  - 语言服务(2个): 机器翻译、文本纠错
  - 身份安全(1个): 人脸识别

### 系统分层
1. **用户接入层**: 企业HR、求职者、面试官、管理员
2. **前端应用层**: Web管理端、UniApp移动端、小程序端
3. **API网关层**: 统一网关、认证授权、流量控制、监控日志
4. **业务服务层**: 智能面试、简历分析、用户管理、岗位管理、报告生成
5. **AI能力层**: 多模态分析引擎 + 科大讯飞API集成
6. **基础设施层**: MySQL、Redis、MinIO、Elasticsearch、Nginx、Docker
7. **运维支撑**: 监控告警、日志分析、配置中心、消息队列、CI/CD

## 功能模块

### 企业端功能
- **企业管理**: 企业信息、组织架构、权限管理
- **岗位管理**: 岗位发布、面试配置、评测标准设定
- **面试管理**: 实时监控、候选人状态、流程管理
- **数据看板**: 招聘统计、候选人分析、效果评估
- **候选人管理**: 信息管理、评测报告、招聘决策

### 求职者端功能
- **用户中心**: 注册登录、信息管理、简历上传
- **岗位浏览**: 招聘岗位、要求查看、流程了解
- **模拟面试**: AI面试体验、多模态交互
- **历史记录**: 面试记录、进度跟踪、结果查看
- **个性化反馈**: 评测报告、改进建议

### 核心AI功能
- **多模态分析**: 语音+视频+文本三维度融合分析
- **动态追问**: 基于回答内容的智能追问机制
- **实时情感计算**: 面试过程中的情感状态分析
- **智能简历分析**: 自动解析、技能匹配、问题生成
- **个性化评测**: 不同岗位的定制化评测标准
- **虚拟面试官**: 语音对话、情感交互、个性化引导

## 创新特色

### 多维度创新
1. **多端开发**: Web + 移动端H5 + 小程序全覆盖
2. **多模块开发**: 智能面试 + 多模态分析 + 可视化报告
3. **多角色开发**: HR + 面试官 + 求职者 + 管理员
4. **多模态融合**: 语音、视频、文本三维度智能融合

### 技术创新
- **多模态融合算法**: 跨模态信息互补增强
- **动态追问机制**: 模拟真实面试官逻辑
- **实时情感计算**: 心理状态实时分析
- **心理学理论融合**: 大五人格模型、情商理论等

### 安全与隐私
- **数据加密**: AES-256加密算法
- **传输安全**: HTTPS协议
- **权限控制**: 完善的权限体系
- **隐私保护**: 数据脱敏、匿名化处理

## 项目文件结构

### 根目录结构
```
├── backend/                 # 后端代码
│   ├── RuoYi-Vue-Plus-5.X/  # 微服务版本
│   └── RuoYi-Vue-Plus-Single/ # 单体版本
├── front/                   # 前端代码
│   ├── plus-ui/             # Web管理端
│   └── unibest-main/        # UniApp移动端
├── doc/                     # 项目文档
│   ├── 前端页面文档/         # 页面设计文档
│   ├── 原型/                # 原型设计文件
│   └── 系统设计文档.html     # 系统设计说明
└── 记忆与任务/              # 项目记忆与任务管理
```

### 关键文档
- `概要说明文档.html`: 项目整体概述
- `多模态智能评测系统架构图.html`: 系统架构可视化
- `系统设计文档.html`: 详细技术设计
- `用户使用说明书.html`: 用户操作指南
- `部署文档.html`: 系统部署指南
- `测试报告.html`: 测试结果报告

## 开发规范

### 代码规范
- 前端: Vue3 + TypeScript + ESLint
- 后端: Spring Boot + MyBatis-Plus + 阿里巴巴代码规范
- 数据库: 驼峰命名 + 索引优化
- API: RESTful设计 + 统一响应格式

### 文档规范
- HTML文档: 宋体小四字体，1.75倍行距
- 架构图: 横向布局，翡翠绿主题色
- 用户手册: 详细步骤说明，图文并茂

## 项目状态
- **当前版本**: v1.0
- **开发状态**: 完成开发，进入文档完善阶段
- **部署状态**: 支持Docker容器化部署
- **测试状态**: 功能测试完成，性能测试进行中

## 关键决策记录
1. **架构选择**: 选择RuoYi-Vue-Plus作为基础框架，快速构建企业级应用
2. **AI平台**: 深度集成科大讯飞平台，确保AI能力的专业性和稳定性
3. **多端策略**: 采用Uni-app实现一套代码多端运行，降低开发成本
4. **数据安全**: 严格遵循数据保护法规，实施多层次安全防护

## 待办事项
- [ ] 完善API文档
- [ ] 优化性能测试
- [ ] 补充用户培训材料
- [ ] 准备演示环境
